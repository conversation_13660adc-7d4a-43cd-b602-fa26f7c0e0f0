package com.videoplayer.controller;

import com.videoplayer.entity.Video;
import com.videoplayer.service.VideoService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 视频控制器
 * 提供视频相关的REST API接口
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/videos")
@CrossOrigin(origins = "*")
public class VideoController {

    @Autowired
    private VideoService videoService;
    

    
    /**
     * 获取视频的完整URL
     */
    private String getFullVideoUrl(Video video) {
        return videoService.getVideoUrl(video.getVideoUrl());
    }

    /**
     * 获取所有视频列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getAllVideos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        try {
            Page<Video> videoPage = videoService.getAllActiveVideos(page, size, sortBy, sortDir);
            
            List<Video> videos = videoPage.getContent();
            // 处理每个视频的URL
            videos.forEach(video -> {
                video.setVideoUrl(getFullVideoUrl(video));
                if (video.getThumbnailUrl() != null) {
                    video.setThumbnailUrl(videoService.getVideoUrl(video.getThumbnailUrl()));
                }
            });
            
            Map<String, Object> response = new HashMap<>();
            response.put("videos", videos);
            response.put("currentPage", videoPage.getNumber());
            response.put("totalItems", videoPage.getTotalElements());
            response.put("totalPages", videoPage.getTotalPages());
            response.put("success", true);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取视频列表失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 根据ID获取视频详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getVideoById(@PathVariable Long id) {
        try {
            Optional<Video> optionalVideo = videoService.getVideoById(id);
            Map<String, Object> response = new HashMap<>();
            
            if (optionalVideo.isPresent()) {
                Video video = optionalVideo.get();
                // 处理视频URL
                video.setVideoUrl(getFullVideoUrl(video));
                if (video.getThumbnailUrl() != null) {
                    video.setThumbnailUrl(videoService.getVideoUrl(video.getThumbnailUrl()));
                }
                response.put("video", video);
                response.put("success", true);
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "视频不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取视频详情失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 添加新视频
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createVideo(@Valid @RequestBody Video video) {
        try {
            // 验证视频URL
            if (!videoService.isValidVideoUrl(video.getVideoUrl())) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "无效的视频URL格式");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
            }
            
            Video savedVideo = videoService.saveVideo(video);
            
            Map<String, Object> response = new HashMap<>();
            response.put("video", savedVideo);
            response.put("success", true);
            response.put("message", "视频添加成功");
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "添加视频失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 更新视频信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateVideo(@PathVariable Long id, @Valid @RequestBody Video videoDetails) {
        try {
            Video updatedVideo = videoService.updateVideo(id, videoDetails);
            Map<String, Object> response = new HashMap<>();
            
            if (updatedVideo != null) {
                response.put("video", updatedVideo);
                response.put("success", true);
                response.put("message", "视频更新成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "视频不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "更新视频失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 删除视频
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteVideo(@PathVariable Long id) {
        try {
            boolean deleted = videoService.deleteVideo(id);
            Map<String, Object> response = new HashMap<>();
            
            if (deleted) {
                response.put("success", true);
                response.put("message", "视频删除成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "视频不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除视频失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 播放视频（保留接口但不做播放次数统计）
     */
    @PostMapping("/{id}/play")
    public ResponseEntity<Map<String, Object>> playVideo(@PathVariable Long id) {
        try {
            Optional<Video> video = videoService.getVideoById(id);
            Map<String, Object> response = new HashMap<>();

            if (video.isPresent()) {
                response.put("success", true);
                response.put("message", "视频播放成功");
                return ResponseEntity.ok(response);
            } else {
                response.put("success", false);
                response.put("message", "视频不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "播放视频失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 搜索视频
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchVideos(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        try {
            Page<Video> videoPage = videoService.searchVideos(keyword, page, size);
            
            Map<String, Object> response = new HashMap<>();
            response.put("videos", videoPage.getContent());
            response.put("currentPage", videoPage.getNumber());
            response.put("totalItems", videoPage.getTotalElements());
            response.put("totalPages", videoPage.getTotalPages());
            response.put("keyword", keyword);
            response.put("success", true);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "搜索视频失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取最新视频
     */
    @GetMapping("/popular")
    public ResponseEntity<Map<String, Object>> getPopularVideos(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<Video> videos = videoService.getPopularVideos(limit);

            Map<String, Object> response = new HashMap<>();
            response.put("videos", videos);
            response.put("success", true);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取最新视频失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * 获取视频统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getVideoStats() {
        try {
            long totalCount = videoService.getTotalVideoCount();
            
            Map<String, Object> response = new HashMap<>();
            response.put("totalVideos", totalCount);
            response.put("success", true);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "获取统计信息失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
}

