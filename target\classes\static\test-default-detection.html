<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>默认联系人检测测试</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .contact-list {
            margin-top: 20px;
        }
        .contact-item {
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .contact-item.default {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>默认联系人检测测试</h1>
        
        <div>
            <button onclick="testAPI()">测试API数据</button>
            <button onclick="testDefaultDetection()">测试默认检测</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div id="results"></div>
        <div id="contactList" class="contact-list"></div>
    </div>

    <script>
        let testData = [];
        
        // 测试API数据
        async function testAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="test-result info">正在获取API数据...</div>';
            
            try {
                const response = await fetch('/api/lead-contacts/all');
                const data = await response.json();
                
                if (data.success && data.data) {
                    testData = data.data;
                    resultsDiv.innerHTML = `
                        <div class="test-result success">
                            ✅ API数据获取成功<br>
                            总数: ${data.total}<br>
                            默认联系人ID: ${data.defaultContactId}
                        </div>
                    `;
                    
                    displayContacts();
                } else {
                    resultsDiv.innerHTML = '<div class="test-result error">❌ API数据获取失败</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="test-result error">❌ 请求失败: ${error.message}</div>`;
            }
        }
        
        // 显示联系人列表
        function displayContacts() {
            const listDiv = document.getElementById('contactList');
            if (testData.length === 0) {
                listDiv.innerHTML = '<div class="test-result info">没有联系人数据</div>';
                return;
            }
            
            let html = '<h3>联系人列表</h3>';
            testData.forEach((contact, index) => {
                const isDefault = contact.id === 1 || contact.isDefault;
                html += `
                    <div class="contact-item ${isDefault ? 'default' : ''}">
                        <strong>索引 ${index}:</strong> 
                        ID=${contact.id}, 
                        姓名=${contact.contactName}, 
                        电话=${contact.phone}
                        ${isDefault ? ' <strong>[默认]</strong>' : ''}
                    </div>
                `;
            });
            listDiv.innerHTML = html;
        }
        
        // 测试默认检测逻辑
        function testDefaultDetection() {
            const resultsDiv = document.getElementById('results');
            
            if (testData.length === 0) {
                resultsDiv.innerHTML = '<div class="test-result error">❌ 请先获取API数据</div>';
                return;
            }
            
            let defaultIndex = -1;
            let detectionMethod = '';
            
            // 方法1：查找ID为1的默认联系人
            for (let i = 0; i < testData.length; i++) {
                const contact = testData[i];
                if (contact.id === 1) {
                    defaultIndex = i;
                    detectionMethod = `通过ID=1找到默认联系人: ${contact.contactName}`;
                    break;
                }
            }
            
            // 方法2：查找具有 isDefault 标记的联系信息
            if (defaultIndex === -1) {
                for (let i = 0; i < testData.length; i++) {
                    const contact = testData[i];
                    if (contact.isDefault || contact.is_default) {
                        defaultIndex = i;
                        detectionMethod = `通过isDefault标记找到默认联系人: ${contact.contactName}`;
                        break;
                    }
                }
            }
            
            // 方法3：使用第一个项目
            if (defaultIndex === -1) {
                defaultIndex = 0;
                detectionMethod = '使用第一个联系人作为默认';
            }
            
            const defaultContact = testData[defaultIndex];
            resultsDiv.innerHTML = `
                <div class="test-result success">
                    ✅ 默认检测完成<br>
                    检测方法: ${detectionMethod}<br>
                    默认索引: ${defaultIndex}<br>
                    默认联系人: ${defaultContact.contactName} (ID: ${defaultContact.id})<br>
                    电话: ${defaultContact.phone}
                </div>
            `;
            
            // 高亮显示默认联系人
            displayContacts();
        }
        
        // 清除结果
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('contactList').innerHTML = '';
            testData = [];
        }
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            testAPI();
        });
    </script>
</body>
</html>
