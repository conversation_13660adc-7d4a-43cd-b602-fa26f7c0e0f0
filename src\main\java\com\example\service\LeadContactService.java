package com.example.service;

import com.example.entity.LeadContact;
import com.example.repository.LeadContactRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Lead联系信息业务逻辑层
 */
@Service
@Transactional
public class LeadContactService {
    
    @Autowired
    private LeadContactRepository leadContactRepository;
    
    /**
     * 获取所有启用的联系信息（用于轮播显示）
     */
    public List<LeadContact> getAllEnabledContacts() {
        return leadContactRepository.findAllEnabledOrderByDisplayOrder();
    }
    
    /**
     * 获取轮播显示的联系信息（限制数量）
     */
    public List<LeadContact> getCarouselContacts(int limit) {
        if (limit <= 0) {
            return getAllEnabledContacts();
        }
        return leadContactRepository.findTopEnabledContacts(PageRequest.of(0, limit));
    }
    
    /**
     * 获取默认显示的联系信息
     */
    public Optional<LeadContact> getDefaultContact() {
        return leadContactRepository.findDefaultContact();
    }
    
    /**
     * 根据ID获取联系信息
     */
    public Optional<LeadContact> getContactById(Long id) {
        return leadContactRepository.findById(id);
    }
    
    /**
     * 保存联系信息
     */
    public LeadContact saveContact(LeadContact contact) {
        // 如果没有设置显示顺序，自动设置为最大值+1
        if (contact.getDisplayOrder() == null || contact.getDisplayOrder() == 0) {
            Integer maxOrder = leadContactRepository.findMaxDisplayOrder();
            contact.setDisplayOrder(maxOrder + 1);
        }
        
        return leadContactRepository.save(contact);
    }
    
    /**
     * 更新联系信息
     */
    public LeadContact updateContact(LeadContact contact) {
        return leadContactRepository.save(contact);
    }
    
    /**
     * 删除联系信息（软删除，设置为禁用）
     */
    public void disableContact(Long id) {
        Optional<LeadContact> contactOpt = leadContactRepository.findById(id);
        if (contactOpt.isPresent()) {
            LeadContact contact = contactOpt.get();
            contact.setIsEnabled(false);
            leadContactRepository.save(contact);
        }
    }
    
    /**
     * 物理删除联系信息
     */
    public void deleteContact(Long id) {
        leadContactRepository.deleteById(id);
    }
    
    /**
     * 设置默认联系信息
     */
    public void setDefaultContact(Long id) {
        // 先清除所有默认设置
        List<LeadContact> allContacts = leadContactRepository.findAll();
        for (LeadContact contact : allContacts) {
            if (contact.getIsDefault()) {
                contact.setIsDefault(false);
                leadContactRepository.save(contact);
            }
        }
        
        // 设置新的默认联系信息
        Optional<LeadContact> contactOpt = leadContactRepository.findById(id);
        if (contactOpt.isPresent()) {
            LeadContact contact = contactOpt.get();
            contact.setIsDefault(true);
            contact.setIsEnabled(true); // 确保默认联系信息是启用的
            leadContactRepository.save(contact);
        }
    }
    
    /**
     * 调整显示顺序
     */
    public void updateDisplayOrder(Long id, Integer newOrder) {
        Optional<LeadContact> contactOpt = leadContactRepository.findById(id);
        if (contactOpt.isPresent()) {
            LeadContact contact = contactOpt.get();
            contact.setDisplayOrder(newOrder);
            leadContactRepository.save(contact);
        }
    }
    
    /**
     * 批量更新显示顺序
     */
    public void batchUpdateDisplayOrder(List<Long> contactIds) {
        for (int i = 0; i < contactIds.size(); i++) {
            updateDisplayOrder(contactIds.get(i), i + 1);
        }
    }
    
    /**
     * 根据联系人姓名搜索
     */
    public List<LeadContact> searchByContactName(String contactName) {
        return leadContactRepository.findByContactNameContainingAndIsEnabledTrue(contactName);
    }
    
    /**
     * 检查电话号码是否已存在
     */
    public boolean isPhoneExists(String phone) {
        return leadContactRepository.findByPhoneAndIsEnabledTrue(phone).isPresent();
    }
    
    /**
     * 获取启用的联系信息数量
     */
    public Long getEnabledContactCount() {
        return leadContactRepository.countEnabledContacts();
    }
    
    /**
     * 初始化默认数据（如果数据库为空）
     */
    public void initializeDefaultData() {
        Long count = getEnabledContactCount();
        if (count == 0) {
            // 创建默认的联系信息
            LeadContact contact1 = new LeadContact("林佳", "18722880704", "18722880704", "黄林佳：皮皮管理", "黄林佳：皮皮管理");
            contact1.setDisplayOrder(1);
            contact1.setIsDefault(false);
            saveContact(contact1);
            
            LeadContact contact2 = new LeadContact("黄超", "18057722960", "18057722960", "黄超(黄小燕弟弟)", "黄超(黄小燕弟弟)");
            contact2.setDisplayOrder(2);
            contact2.setIsDefault(true); // 设置为默认
            saveContact(contact2);
            
            LeadContact contact3 = new LeadContact("小班", "15908542510", "15908542510", "佳茵轻康SOS小班", "佳茵轻康SOS小班");
            contact3.setDisplayOrder(3);
            contact3.setIsDefault(false);
            saveContact(contact3);
        }
    }
}
