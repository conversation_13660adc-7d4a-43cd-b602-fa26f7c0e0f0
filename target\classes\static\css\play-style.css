/**
 * 视频播放页面专用样式
 * Video Play Page Styles
 */

/* Video.js播放器样式 */
.video-js {
    width: 100%;
    height: 100%;
    background-color: #000;
}

/* 大播放按钮样式 */
.video-js .vjs-big-play-button {
    background-color: rgba(0, 0, 0, 0.45);
    border: 2px solid #fff;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    line-height: 60px;
    top: 50%;
    left: 50%;
    margin-top: -30px;
    margin-left: -30px;
    transition: all 0.3s ease;
}

/* 大播放按钮悬停效果 */
.video-js:hover .vjs-big-play-button {
    background-color: rgba(0, 0, 0, 0.65);
    transform: scale(1.1);
}

/* 控制栏样式 */
.video-js .vjs-control-bar {
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

/* 播放器容器 */
.video-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

/* 视频信息区域 */
.video-info {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-top: 1rem;
}

/* 视频标题 */
.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

/* 视频描述 */
.video-description {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* 视频元数据 */
.video-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.video-meta-item {
    display: flex;
    align-items: center;
    color: #6c757d;
    font-size: 0.875rem;
}

.video-meta-item i {
    margin-right: 0.5rem;
    color: #007bff;
}

/* 操作按钮区域 */
.video-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

/* 全屏按钮 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0,0,0,0.9);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .video-js .vjs-big-play-button {
        width: 50px;
        height: 50px;
        line-height: 50px;
        margin-top: -25px;
        margin-left: -25px;
    }
    
    .video-info {
        padding: 1rem;
        margin-top: 0.5rem;
    }
    
    .video-title {
        font-size: 1.25rem;
    }
    
    .video-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .video-actions {
        flex-direction: column;
    }
}

/* 加载状态 */
.video-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background: #f8f9fa;
    border-radius: 8px;
}

.video-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 错误状态 */
.video-error {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.video-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dc3545;
}

/* 播放器控制增强 */
.video-js .vjs-progress-control {
    height: 6px;
}

.video-js .vjs-play-progress {
    background: #007bff;
}

.video-js .vjs-volume-panel {
    margin-right: 10px;
}

/* 自定义播放速度菜单 */
.video-js .vjs-playback-rate .vjs-playback-rate-value {
    font-size: 1.2em;
    line-height: 1.5;
}

/* 画中画按钮 */
.video-js .vjs-picture-in-picture-control {
    order: 7;
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-info {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .video-title {
        color: #f7fafc;
    }
    
    .video-description {
        color: #a0aec0;
    }
    
    .video-meta {
        border-top-color: #4a5568;
    }
    
    .video-meta-item {
        color: #a0aec0;
    }
}
