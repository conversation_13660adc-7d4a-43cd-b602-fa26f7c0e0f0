<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系信息轮播测试</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="/css/index-style.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .test-container { max-width: 800px; margin: 0 auto; }
        .info-panel { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .contact-list { background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
        .contact-item { padding: 10px; border-bottom: 1px solid #eee; }
        .contact-item:last-child { border-bottom: none; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>联系信息轮播测试页面</h1>
        
        <div class="info-panel">
            <h3>📊 数据统计</h3>
            <p><strong>数据库联系信息数量:</strong> <span id="dbCount">加载中...</span></p>
            <p><strong>前端显示数量:</strong> <span id="frontendCount">加载中...</span></p>
            <p><strong>状态:</strong> <span id="status">初始化中...</span></p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h3>🎨 3D轮播效果</h3>
                <div class="lead-carousel" style="height: 200px; background: #f0f0f0; border-radius: 10px;">
                    <div class="lead-scroll-container">
                        <!-- 联系信息将在这里显示 -->
                    </div>
                    <div class="lead-indicators">
                        <!-- 指示器将在这里显示 -->
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>📋 数据列表</h3>
                <div class="contact-list" id="contactList">
                    <p>加载中...</p>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button class="btn btn-info" onclick="testAPI()">🧪 测试API</button>
            <button class="btn btn-success" onclick="addTestContact()">➕ 添加测试联系人</button>
            <button class="btn btn-warning" onclick="reloadPage()">🔄 重新加载页面</button>
        </div>
        
        <div id="results" class="mt-4"></div>
    </div>

    <script src="/js/lead-carousel-db.js"></script>
    <script>
        let carousel;
        
        // 初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 页面加载完成，开始初始化轮播');
            
            // 初始化轮播组件
            carousel = new LeadCarouselDB();
            await carousel.init();
            
            // 更新统计信息
            updateStats();
            
            // 显示联系信息列表
            displayContactList();
        });
        
        // 更新统计信息
        async function updateStats() {
            try {
                const response = await fetch('/api/lead-contacts/all');
                const data = await response.json();
                
                document.getElementById('dbCount').textContent = data.total || 0;
                document.getElementById('frontendCount').textContent = carousel ? carousel.contactData.length : 0;
                document.getElementById('status').textContent = data.success ? '✅ 正常' : '❌ 异常';
                
            } catch (error) {
                document.getElementById('status').textContent = '❌ 连接失败';
                console.error('获取统计信息失败:', error);
            }
        }
        
        // 显示联系信息列表
        async function displayContactList() {
            try {
                const response = await fetch('/api/lead-contacts/all');
                const data = await response.json();
                
                const listContainer = document.getElementById('contactList');
                
                if (data.success && data.data.length > 0) {
                    listContainer.innerHTML = data.data.map((contact, index) => `
                        <div class="contact-item">
                            <strong>${index + 1}. ${contact.contactName}</strong> (${contact.contactType})
                            <br>📞 ${contact.phone} | 💬 ${contact.wechat} | 🎵 ${contact.douyinNickname}
                            ${contact.isDefault ? '<span class="badge bg-primary">默认</span>' : ''}
                        </div>
                    `).join('');
                } else {
                    listContainer.innerHTML = '<p class="text-danger">没有找到联系信息</p>';
                }
                
            } catch (error) {
                document.getElementById('contactList').innerHTML = '<p class="text-danger">加载失败: ' + error.message + '</p>';
            }
        }
        
        // 重新加载页面
        function reloadPage() {
            document.getElementById('results').innerHTML = '<div class="alert alert-info">正在重新加载页面...</div>';
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
        
        // 测试API
        async function testAPI() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="alert alert-info">正在测试API...</div>';
            
            try {
                const response = await fetch('/api/lead-contacts/all');
                const data = await response.json();
                
                results.innerHTML = `
                    <div class="alert alert-success">
                        <h5>✅ API测试成功</h5>
                        <p><strong>返回数据数量:</strong> ${data.total}</p>
                        <p><strong>成功状态:</strong> ${data.success}</p>
                        <p><strong>默认联系人ID:</strong> ${data.defaultContactId || '无'}</p>
                        <details>
                            <summary>查看详细数据</summary>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </details>
                    </div>
                `;
                
            } catch (error) {
                results.innerHTML = `<div class="alert alert-danger">❌ API测试失败: ${error.message}</div>`;
            }
        }
        
        // 添加测试联系人
        async function addTestContact() {
            const results = document.getElementById('results');
            results.innerHTML = '<div class="alert alert-info">正在添加测试联系人...</div>';
            
            const newContact = {
                contactName: '测试联系人' + Date.now(),
                phone: '1' + Math.floor(Math.random() * 9000000000 + 1000000000),
                wechat: 'test_' + Date.now(),
                douyin: 'test_douyin_' + Date.now(),
                douyinNickname: '测试抖音昵称',
                contactType: '测试',
                isDefault: false,
                sortOrder: 99
            };
            
            try {
                const response = await fetch('/api/lead-contacts', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(newContact)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    results.innerHTML = '<div class="alert alert-success">✅ 测试联系人添加成功！正在刷新数据...</div>';
                    setTimeout(refreshData, 1000);
                } else {
                    results.innerHTML = '<div class="alert alert-danger">❌ 添加失败: ' + data.message + '</div>';
                }
                
            } catch (error) {
                results.innerHTML = '<div class="alert alert-danger">❌ 添加失败: ' + error.message + '</div>';
            }
        }
    </script>
</body>
</html>
