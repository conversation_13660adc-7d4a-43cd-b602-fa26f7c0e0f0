/**
 * Lead 3D滚筒组件
 * 实现联系信息的3D滚筒选择效果
 * <AUTHOR>
 * @version 3.0.0
 */

class LeadCarousel {
    constructor() {
        this.currentIndex = 1; // 从中间项开始
        this.defaultIndex = 1; // 默认显示的项目索引
        this.items = [];

        this.container = null;
        this.rotationAngle = 300; // 每项旋转角度
        this.translateZDistance = 100; // 3D深度距离 - 控制项目间的前后间距
        this.isDragging = false; // 仅用于触摸事件
        this.currentRotation = 0;

        this.init();
    }

    /**
     * 初始化3D滚筒组件
     */
    init() {
        // 获取DOM元素
        this.items = document.querySelectorAll('.lead-item');

        this.container = document.querySelector('.lead-scroll-container');

        if (this.items.length === 0) {
            console.warn('Lead carousel: No items found');
            return;
        }

        // 检测默认项目
        this.detectDefaultItem();

        // 生成指示器
        this.generateIndicators();

        // 设置初始状态
        this.updateCylinderStates();
        this.updateRotation();

        // 绑定事件
        this.bindEvents();

        console.log('Lead 3D cylinder initialized with', this.items.length, 'items (manual control only)');
    }

    /**
     * 生成指示器
     */
    generateIndicators() {
        const indicatorsContainer = document.querySelector('.lead-indicators');
        if (!indicatorsContainer) {
            console.warn('Lead carousel: Indicators container not found');
            return;
        }

        // 清空现有指示器
        indicatorsContainer.innerHTML = '';

        // 生成3个指示器（上、中、下）
        for (let i = 0; i < 3; i++) {
            const indicator = document.createElement('span');
            indicator.className = 'indicator';
            indicator.dataset.index = i;

            if (i === 1) {
                // 中间指示器始终为激活状态
                indicator.classList.add('active');
                indicator.title = '点击恢复默认值';
            } else {
                // 上下指示器为导航按钮
                indicator.classList.add('nav-button');
                indicator.title = i === 0 ? '上一个' : '下一个';
            }

            indicatorsContainer.appendChild(indicator);
        }

        console.log('Generated 3 indicators for lead carousel');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 指示器导航控制事件
        const indicators = document.querySelectorAll('.indicator');

        if (indicators.length === 0) {
            console.warn('Lead carousel: No indicators found for event binding');
            return;
        }

        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                console.log(`Indicator ${index} clicked`);

                if (index === 0) {
                    // 最上面的点：切换到上一个内容
                    console.log('Previous slide triggered');
                    this.previousSlide();
                } else if (index === 1) {
                    // 中间的点：恢复显示默认值
                    console.log('Reset to default triggered');
                    this.resetToDefault();
                } else if (index === 2) {
                    // 最下面的点：切换到下一个内容
                    console.log('Next slide triggered');
                    this.nextSlide();
                }
            });
        });

        console.log(`Bound events to ${indicators.length} indicators`);

        // 键盘导航（上下箭头）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.previousSlide();
            } else if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.nextSlide();
            }
        });

        // 触摸滑动支持
        this.addTouchSupport();
    }

    /**
     * 添加触摸滑动支持（仅触摸设备）
     */
    addTouchSupport() {
        const carousel = document.querySelector('.lead-carousel');
        if (!carousel) return;

        let startY = 0;
        let currentY = 0;
        let initialRotation = 0;

        // 触摸事件（保留）
        carousel.addEventListener('touchstart', (e) => {
            this.isDragging = true;
            startY = e.touches[0].clientY;
            initialRotation = this.currentRotation;
        });

        carousel.addEventListener('touchmove', (e) => {
            if (!this.isDragging) return;

            currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;
            const rotationDelta = deltaY * 0.5;

            if (this.container) {
                this.container.style.transform = `rotateX(${initialRotation + rotationDelta}deg)`;
            }
            e.preventDefault();
        });

        carousel.addEventListener('touchend', () => {
            if (!this.isDragging) return;

            this.isDragging = false;

            const deltaY = currentY - startY;
            if (Math.abs(deltaY) > 30) {
                if (deltaY > 0) {
                    this.previousSlide();
                } else {
                    this.nextSlide();
                }
            } else {
                this.updateRotation();
            }
        });
    }

    /**
     * 更新3D滚筒状态 - 循环布局
     */
    updateCylinderStates() {
        this.items.forEach((item, index) => {
            // 清除所有状态类
            item.classList.remove('cylinder-top', 'cylinder-center', 'cylinder-bottom');

            // 重置内联样式
            item.style.opacity = '';
            item.style.transform = '';

            // 计算循环位置：1=top, 2=center, 3=bottom
            let position;
            if (index === this.currentIndex) {
                position = 2; // 当前项在中心位置
            } else if (index === (this.currentIndex - 1 + this.items.length) % this.items.length) {
                position = 1; // 上一项在上方位置
            } else if (index === (this.currentIndex + 1) % this.items.length) {
                position = 3; // 下一项在下方位置
            } else {
                // 其他项目隐藏（如果有超过3个项目）
                item.style.opacity = '0';
                item.style.transform = `rotateX(180deg) translateZ(${this.translateZDistance}px)`;
                return;
            }

            // 应用对应的状态类
            if (position === 1) {
                item.classList.add('cylinder-top');
            } else if (position === 2) {
                item.classList.add('cylinder-center');
            } else if (position === 3) {
                item.classList.add('cylinder-bottom');
            }
        });

        // 同时更新指示器状态
        this.updateIndicators();
    }

    /**
     * 更新指示器状态 - 导航控制模式
     */
    updateIndicators() {
        const indicators = document.querySelectorAll('.indicator');
        indicators.forEach((indicator, index) => {
            indicator.classList.remove('active', 'nav-button');

            if (index === 1) {
                // 中间的点始终显示为当前激活状态
                indicator.classList.add('active');
            } else {
                // 上下两个点显示为导航按钮
                indicator.classList.add('nav-button');
            }
        });
    }

    /**
     * 更新3D旋转
     */
    updateRotation() {
        if (!this.container) return;

        // 计算旋转角度
        this.currentRotation = -this.currentIndex * this.rotationAngle;
        this.container.style.transform = `rotateX(${this.currentRotation}deg)`;
    }

    /**
     * 切换到指定项目
     * @param {number} index - 项目索引
     */
    goToSlide(index) {
        if (index < 0 || index >= this.items.length || index === this.currentIndex) {
            return;
        }

        this.currentIndex = index;
        this.updateCylinderStates();
        this.updateRotation();
    }

    /**
     * 下一张幻灯片
     */
    nextSlide() {
        const nextIndex = (this.currentIndex + 1) % this.items.length;
        this.goToSlide(nextIndex);
    }

    /**
     * 带增强视觉效果的下一张幻灯片
     * @param {Element} indicator - 触发的指示器元素
     */
    nextSlideWithEffect(indicator) {
        // 添加下切换点击效果到指示器
        this.addDownClickEffect(indicator);

        // 执行实际的切换
        const nextIndex = (this.currentIndex + 1) % this.items.length;
        this.goToSlide(nextIndex);

        // 立即添加下切换过渡效果
        this.addSlideDownTransition();
    }

    /**
     * 上一张幻灯片
     */
    previousSlide() {
        const prevIndex = (this.currentIndex - 1 + this.items.length) % this.items.length;
        this.goToSlide(prevIndex);
    }

    /**
     * 带增强视觉效果的上一张幻灯片
     * @param {Element} indicator - 触发的指示器元素
     */
    previousSlideWithEffect(indicator) {
        // 添加上切换点击效果到指示器
        this.addUpClickEffect(indicator);

        // 执行实际的切换
        const prevIndex = (this.currentIndex - 1 + this.items.length) % this.items.length;
        this.goToSlide(prevIndex);

        // 立即添加上切换过渡效果
        this.addSlideUpTransition();
    }

    /**
     * 添加上切换点击效果到指示器
     * @param {Element} indicator - 指示器元素
     */
    addUpClickEffect(indicator) {
        if (!indicator) return;

        // 移除之前的效果类
        indicator.classList.remove('up-click');

        // 强制重绘
        indicator.offsetHeight;

        // 添加上切换点击效果
        indicator.classList.add('up-click');

        // 400ms后移除效果类
        setTimeout(() => {
            indicator.classList.remove('up-click');
        }, 400);
    }

    /**
     * 添加下切换点击效果到指示器
     * @param {Element} indicator - 指示器元素
     */
    addDownClickEffect(indicator) {
        if (!indicator) return;

        // 移除之前的效果类
        indicator.classList.remove('down-click');

        // 强制重绘
        indicator.offsetHeight;

        // 添加下切换点击效果
        indicator.classList.add('down-click');

        // 400ms后移除效果类
        setTimeout(() => {
            indicator.classList.remove('down-click');
        }, 400);
    }

    /**
     * 添加上切换滑动过渡效果
     */
    addSlideUpTransition() {
        const carousel = document.querySelector('.lead-carousel');
        if (!carousel) return;

        // 移除之前的效果类
        carousel.classList.remove('up-transition', 'down-transition');

        // 强制重绘
        carousel.offsetHeight;

        // 添加上切换过渡效果
        carousel.classList.add('up-transition');

        console.log('Applied slide up transition effect');

        // 600ms后移除效果类
        setTimeout(() => {
            carousel.classList.remove('up-transition');
        }, 600);
    }

    /**
     * 添加下切换滑动过渡效果
     */
    addSlideDownTransition() {
        const carousel = document.querySelector('.lead-carousel');
        if (!carousel) return;

        // 移除之前的效果类
        carousel.classList.remove('up-transition', 'down-transition');

        // 强制重绘
        carousel.offsetHeight;

        // 添加下切换过渡效果
        carousel.classList.add('down-transition');

        console.log('Applied slide down transition effect');

        // 600ms后移除效果类
        setTimeout(() => {
            carousel.classList.remove('down-transition');
        }, 600);
    }

    /**
     * 检测默认项目
     * 查找具有默认标识的项目，如果没有则使用中间项目
     */
    detectDefaultItem() {
        // 方法1：查找具有 data-default="true" 属性的项目
        for (let i = 0; i < this.items.length; i++) {
            if (this.items[i].dataset.default === 'true') {
                this.defaultIndex = i;
                console.log('Found default item at index:', i);
                return;
            }
        }

        // 方法2：查找具有 'default' 类的项目
        for (let i = 0; i < this.items.length; i++) {
            if (this.items[i].classList.contains('default')) {
                this.defaultIndex = i;
                console.log('Found default item with class at index:', i);
                return;
            }
        }

        // 方法3：查找文本内容包含"默认"的项目
        for (let i = 0; i < this.items.length; i++) {
            const text = this.items[i].textContent || '';
            if (text.includes('默认') || text.includes('主要') || text.includes('推荐')) {
                this.defaultIndex = i;
                console.log('Found default item by text at index:', i);
                return;
            }
        }

        // 默认使用中间项目
        this.defaultIndex = Math.floor(this.items.length / 2);
        console.log('Using middle item as default at index:', this.defaultIndex);
    }

    /**
     * 恢复到默认值
     * 点击中间指示器时调用
     */
    resetToDefault() {
        if (this.currentIndex === this.defaultIndex) {
            // 如果当前已经是默认项目，添加一个视觉反馈
            this.showResetFeedback();
            console.log('Already at default item');
        } else {
            // 切换到默认项目
            this.goToSlide(this.defaultIndex);
            this.showResetFeedback();
            console.log('Reset to default item at index:', this.defaultIndex);
        }
    }

    /**
     * 显示重置反馈效果
     * 为用户提供视觉反馈，表明已经恢复到默认值
     */
    showResetFeedback() {
        const currentItem = this.items[this.currentIndex];
        if (!currentItem) return;

        // 添加重置反馈类
        currentItem.classList.add('reset-feedback');

        // 为中间指示器添加反馈效果
        const middleIndicator = document.querySelector('.indicator.active');
        if (middleIndicator) {
            middleIndicator.classList.add('reset-feedback');
        }

        // 1秒后移除反馈效果
        setTimeout(() => {
            currentItem.classList.remove('reset-feedback');
            if (middleIndicator) {
                middleIndicator.classList.remove('reset-feedback');
            }
        }, 1000);
    }

    /**
     * 销毁轮播组件
     */
    destroy() {
        // 移除事件监听器等清理工作
        console.log('Lead carousel destroyed');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否存在lead轮播元素
    if (document.querySelector('.lead-carousel')) {
        window.leadCarousel = new LeadCarousel();
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.leadCarousel) {
        window.leadCarousel.destroy();
    }
});
