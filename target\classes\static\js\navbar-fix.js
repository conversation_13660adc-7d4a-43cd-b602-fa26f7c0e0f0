/**
 * 导航栏搜索功能脚本
 * 实现点击按钮展开/收起搜索框的功能
 */

document.addEventListener('DOMContentLoaded', function() {
    // 初始化搜索功能
    initializeSearchToggle();

    // 确保搜索表单正常工作
    initializeSearchForms();

    // 监听ESC键关闭搜索框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSearch();
        }
    });
});

/**
 * 初始化搜索切换功能
 */
function initializeSearchToggle() {
    // 确保所有搜索容器都正确初始化
    const searchContainers = document.querySelectorAll('.search-container');

    searchContainers.forEach(function(container) {
        const searchForm = container.querySelector('.search-form');
        const searchToggle = container.querySelector('.search-toggle');
        const searchInput = container.querySelector('.search-input-mobile');

        if (searchForm && searchToggle) {
            // 检查是否有搜索内容（如videos页面）
            const hasSearchContent = searchInput && searchInput.value && searchInput.value.trim() !== '';

            if (hasSearchContent) {
                // 如果有搜索内容，保持展开状态
                searchForm.classList.add('active');
                searchToggle.style.display = 'none';
            } else {
                // 默认收起状态
                searchForm.classList.remove('active');
                searchToggle.style.display = 'inline-block';
            }
        }
    });

    console.log('搜索切换功能已初始化');
}

/**
 * 切换搜索框显示/隐藏
 */
function toggleSearch() {
    const searchContainers = document.querySelectorAll('.search-container');

    searchContainers.forEach(function(container) {
        const searchForm = container.querySelector('.search-form');
        const searchToggle = container.querySelector('.search-toggle');
        const searchInput = container.querySelector('.search-input-mobile');

        if (searchForm && searchToggle) {
            if (searchForm.classList.contains('active')) {
                // 收起搜索框
                searchForm.classList.remove('active');
                searchToggle.style.display = 'inline-block';

                // 清空输入框（如果为空）
                if (searchInput && !searchInput.value.trim()) {
                    searchInput.value = '';
                }
            } else {
                // 展开搜索框
                searchForm.classList.add('active');
                searchToggle.style.display = 'none';

                // 聚焦到输入框
                if (searchInput) {
                    setTimeout(() => {
                        searchInput.focus();
                    }, 100);
                }
            }
        }
    });
}

/**
 * 关闭搜索框
 */
function closeSearch() {
    const searchContainers = document.querySelectorAll('.search-container');

    searchContainers.forEach(function(container) {
        const searchForm = container.querySelector('.search-form');
        const searchToggle = container.querySelector('.search-toggle');
        const searchInput = container.querySelector('.search-input-mobile');

        if (searchForm && searchToggle) {
            searchForm.classList.remove('active');
            searchToggle.style.display = 'inline-block';

            // 如果输入框为空，清空它
            if (searchInput && !searchInput.value.trim()) {
                searchInput.value = '';
            }
        }
    });
}

/**
 * 初始化搜索表单
 */
function initializeSearchForms() {
    const searchForms = document.querySelectorAll('.search-form');

    searchForms.forEach(function(form) {
        // 添加表单提交处理
        form.addEventListener('submit', function(e) {
            const input = form.querySelector('input[name="keyword"]');
            if (input && input.value.trim() === '') {
                e.preventDefault();
                alert('请输入搜索关键词');
                input.focus();
                return false;
            }
        });

        // 搜索输入框样式处理
        const input = form.querySelector('input[type="search"]');
        if (input) {
            input.addEventListener('focus', function() {
                this.style.backgroundColor = 'rgba(255,255,255,0.2)';
                this.style.borderColor = 'rgba(255,255,255,0.5)';
            });

            input.addEventListener('blur', function() {
                this.style.backgroundColor = 'rgba(255,255,255,0.1)';
                this.style.borderColor = 'rgba(255,255,255,0.3)';
            });

            // 回车键提交
            input.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    form.submit();
                }
            });
        }
    });
}

/**
 * 调试函数 - 检查搜索容器状态
 */
function debugSearchContainers() {
    const searchContainers = document.querySelectorAll('.search-container');

    console.log('=== 搜索容器调试信息 ===');
    console.log('找到搜索容器数量:', searchContainers.length);

    searchContainers.forEach(function(container, index) {
        const searchForm = container.querySelector('.search-form');
        const searchToggle = container.querySelector('.search-toggle');
        const searchInput = container.querySelector('.search-input-mobile');

        console.log(`搜索容器 ${index + 1}:`);
        console.log('  表单active状态:', searchForm ? searchForm.classList.contains('active') : '未找到');
        console.log('  按钮显示状态:', searchToggle ? searchToggle.style.display : '未找到');
        console.log('  输入框值:', searchInput ? searchInput.value : '未找到');
        console.log('  表单类名:', searchForm ? searchForm.className : '未找到');
        console.log('  容器类名:', container.className);
    });
}

/**
 * 强制展开所有搜索框（调试用）
 */
function showAllSearchForms() {
    const searchContainers = document.querySelectorAll('.search-container');

    searchContainers.forEach(function(container) {
        const searchForm = container.querySelector('.search-form');
        const searchToggle = container.querySelector('.search-toggle');

        if (searchForm && searchToggle) {
            searchForm.classList.add('active');
            searchToggle.style.display = 'none';
        }
    });

    console.log('所有搜索框已展开');
}

/**
 * 强制收起所有搜索框（调试用）
 */
function hideAllSearchForms() {
    const searchContainers = document.querySelectorAll('.search-container');

    searchContainers.forEach(function(container) {
        const searchForm = container.querySelector('.search-form');
        const searchToggle = container.querySelector('.search-toggle');

        if (searchForm && searchToggle) {
            searchForm.classList.remove('active');
            searchToggle.style.display = 'inline-block';
        }
    });

    console.log('所有搜索框已收起');
}

// 导出函数到全局作用域
window.toggleSearch = toggleSearch;
window.closeSearch = closeSearch;
window.debugSearchContainers = debugSearchContainers;
window.showAllSearchForms = showAllSearchForms;
window.hideAllSearchForms = hideAllSearchForms;
