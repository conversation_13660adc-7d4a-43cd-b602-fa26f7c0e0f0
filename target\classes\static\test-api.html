<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>联系信息API测试</h1>
    
    <button onclick="testCarouselAPI()">测试轮播API (limit=0)</button>
    <button onclick="testAllContactsAPI()">测试所有联系信息API (/all)</button>
    <button onclick="testCarouselAPILimited()">测试轮播API (limit=3)</button>
    
    <div id="results"></div>

    <script>
        async function testCarouselAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">正在测试轮播API (获取全部数据)...</div>';

            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                try {
                    controller.abort();
                } catch (error) {
                    // 静默处理abort错误
                }
            }, 5000);

            try {
                const response = await fetch('/api/lead-contacts/carousel?limit=0', {
                    signal: controller.signal
                });
                clearTimeout(timeoutId);
                const data = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 轮播API测试成功</h3>
                            <p><strong>状态:</strong> ${response.status}</p>
                            <p><strong>成功:</strong> ${data.success}</p>
                            <p><strong>数据数量:</strong> ${data.total}</p>
                            <p><strong>默认联系人ID:</strong> ${data.defaultContactId || '无'}</p>
                            <p><strong>消息:</strong> ${data.message}</p>
                            <h4>联系信息数据:</h4>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 轮播API测试失败</h3>
                            <p><strong>状态:</strong> ${response.status}</p>
                            <p><strong>错误:</strong> ${data.message || '未知错误'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 请求超时</h3>
                            <p><strong>错误:</strong> 请求超时，请检查网络连接</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 网络错误</h3>
                            <p><strong>错误:</strong> ${error.message}</p>
                        </div>
                    `;
                }
            }
        }
        
        async function testAllContactsAPI() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">正在测试所有联系信息API...</div>';
            
            try {
                const response = await fetch('/api/lead-contacts');
                const data = await response.json();
                
                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 所有联系信息API测试成功</h3>
                            <p><strong>状态:</strong> ${response.status}</p>
                            <p><strong>成功:</strong> ${data.success}</p>
                            <p><strong>数据数量:</strong> ${data.total}</p>
                            <h4>联系信息数据:</h4>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 所有联系信息API测试失败</h3>
                            <p><strong>状态:</strong> ${response.status}</p>
                            <p><strong>错误:</strong> ${data.message || '未知错误'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        async function testCarouselAPILimited() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<div class="result">正在测试轮播API (限制3条)...</div>';

            try {
                const response = await fetch('/api/lead-contacts/carousel?limit=3');
                const data = await response.json();

                if (response.ok) {
                    resultsDiv.innerHTML = `
                        <div class="result success">
                            <h3>✅ 轮播API测试成功 (限制3条)</h3>
                            <p><strong>状态:</strong> ${response.status}</p>
                            <p><strong>成功:</strong> ${data.success}</p>
                            <p><strong>数据数量:</strong> ${data.total}</p>
                            <p><strong>默认联系人ID:</strong> ${data.defaultContactId || '无'}</p>
                            <p><strong>消息:</strong> ${data.message}</p>
                            <h4>联系信息数据:</h4>
                            <pre>${JSON.stringify(data.data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="result error">
                            <h3>❌ 轮播API测试失败 (限制3条)</h3>
                            <p><strong>状态:</strong> ${response.status}</p>
                            <p><strong>错误:</strong> ${data.message || '未知错误'}</p>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ 网络错误</h3>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }



        // 页面加载时自动测试
        window.addEventListener('load', function() {
            testCarouselAPI();
        });
    </script>
</body>
</html>
