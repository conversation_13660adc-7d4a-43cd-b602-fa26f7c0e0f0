server:
  port: 5000
  servlet:
    context-path: /

spring:
  application:
    name: video-player
  
  # 数据库配置
  datasource:
    url: ******************************************************************************************************************************************************
#    username: video_player
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: false
    
  # Thymeleaf配置
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    
  # 阿里云OSS配置
  cloud:
    alicloud:
      oss:
        endpoint: https://oss-cn-guangzhou.aliyuncs.com
        bucket: jyqk
        dir: video-player/videos
        baseUrl: https://jyqk.oss-cn-guangzhou.aliyuncs.com
    mode: HTML
    encoding: UTF-8
    cache: false
    
  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0
        
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# 日志配置
logging:
  level:
    com.videoplayer: INFO
    org.springframework.web: WARN
    org.hibernate.SQL: WARN
    org.hibernate.type.descriptor.sql.BasicBinder: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 自定义配置
video:
  # 阿里云OSS配置（可选）
  oss:
    endpoint: ""
    access-key-id: ""
    access-key-secret: ""
    bucket-name: ""

