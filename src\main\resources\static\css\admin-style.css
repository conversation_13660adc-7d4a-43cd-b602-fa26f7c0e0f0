/**
 * 管理界面专用样式
 * Admin Page Styles
 */

/* 管理页面头部 */
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

/* Admin页面Lead文字样式 - 清晰专业 */
.admin-header .lead {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    line-height: 1.5;
    color: #ffffff;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.8rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.3);
    display: inline-block;
    margin: 0.5rem 0;
    animation: slideInLeft 0.5s ease-out;
    letter-spacing: 0.2px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

/* Lead悬停效果 */
.admin-header .lead:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.25);
}

/* 动画定义 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式优化 */
@media (max-width: 768px) {
    .admin-header .lead {
        font-size: 0.95rem;
        padding: 0.7rem 1.2rem;
        letter-spacing: 0.1px;
    }
}

@media (max-width: 576px) {
    .admin-header .lead {
        font-size: 0.85rem;
        padding: 0.6rem 1rem;
        letter-spacing: 0.1px;
        line-height: 1.4;
    }
}

/* 统计卡片 */
.stat-card {
    transition: transform 0.2s;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* 视频缩略图 */
.video-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
}

/* 视频标题 */
.video-title {
    font-weight: 600;
    color: #2c3e50;
}

/* 视频描述 */
.video-description {
    color: #6c757d;
    font-size: 0.875rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 操作按钮 */
.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 1px;
}

/* 搜索区域 */
.search-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* 提示容器 */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    min-width: 300px;
}

/* 禁用的视频行 */
.video-row.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-header {
        padding: 1rem 0;
        margin-bottom: 1rem;
    }
    
    .search-section {
        padding: 1rem;
    }
    
    .video-thumbnail {
        width: 60px;
        height: 34px;
    }
    
    .action-btn {
        padding: 0.2rem 0.4rem;
        margin: 0;
    }
    
    .alert-container {
        top: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
    }
}

/* 打印样式 */
@media print {
    .admin-header,
    .search-section,
    .action-btn,
    .loading-overlay,
    .alert-container {
        display: none !important;
    }
    
    .table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
