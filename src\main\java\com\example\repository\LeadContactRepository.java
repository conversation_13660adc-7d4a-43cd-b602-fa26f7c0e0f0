package com.example.repository;

import com.example.entity.LeadContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Lead联系信息数据访问层
 */
@Repository
public interface LeadContactRepository extends JpaRepository<LeadContact, Long> {
    
    /**
     * 查询所有启用的联系信息，按显示顺序排序
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isEnabled = true ORDER BY lc.displayOrder ASC, lc.id ASC")
    List<LeadContact> findAllEnabledOrderByDisplayOrder();
    
    /**
     * 查询默认显示的联系信息
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isEnabled = true AND lc.isDefault = true ORDER BY lc.displayOrder ASC")
    Optional<LeadContact> findDefaultContact();
    
    /**
     * 查询指定数量的启用联系信息
     */
    @Query("SELECT lc FROM LeadContact lc WHERE lc.isEnabled = true ORDER BY lc.displayOrder ASC, lc.id ASC")
    List<LeadContact> findTopEnabledContacts(org.springframework.data.domain.Pageable pageable);
    
    /**
     * 根据显示顺序查询
     */
    List<LeadContact> findByIsEnabledTrueOrderByDisplayOrderAscIdAsc();
    
    /**
     * 查询最大显示顺序
     */
    @Query("SELECT COALESCE(MAX(lc.displayOrder), 0) FROM LeadContact lc")
    Integer findMaxDisplayOrder();
    
    /**
     * 根据联系人姓名查询
     */
    List<LeadContact> findByContactNameContainingAndIsEnabledTrue(String contactName);
    
    /**
     * 根据电话号码查询
     */
    Optional<LeadContact> findByPhoneAndIsEnabledTrue(String phone);
    
    /**
     * 统计启用的联系信息数量
     */
    @Query("SELECT COUNT(lc) FROM LeadContact lc WHERE lc.isEnabled = true")
    Long countEnabledContacts();
}
