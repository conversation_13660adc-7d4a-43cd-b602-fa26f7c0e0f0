<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${pageTitle}">视频播放</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Video.js CSS -->
    <link href="https://vjs.zencdn.net/8.6.1/video-js.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/play-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/admin">
                                <i class="fas fa-cog me-1"></i>管理
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <div class="row">
            <!-- 视频播放区域 -->
            <div class="col-12">
                <div class="video-player-container mb-4 mx-auto" style="max-width: 300px;">
                    <!-- Video.js 播放器 -->
                    <video
                        id="video-player"
                        class="video-js vjs-default-skin vjs-big-play-centered"
                        controls
                        preload="auto"
                        width="100%"
                        height="400"
                        poster=""
                        data-setup='{
                            "fluid": true,
                            "responsive": true,
                            "controls": true,
                            "autoplay": false,
                            "preload": "auto",
                            "playbackRates": [0.5, 1, 1.5, 2]
                        }'
                        th:data-video-url="${video.videoUrl}"
                        th:data-video-id="${video.id}">
                        <source th:src="${video.videoUrl}" type="video/mp4">
                        <p class="vjs-no-js">
                            要播放此视频，请启用JavaScript，并考虑升级到
                            <a href="https://videojs.com/html5-video-support/" target="_blank">
                                支持HTML5视频的Web浏览器
                            </a>。
                        </p>
                    </video>
                </div>

                <!-- 视频信息 -->
                <div class="video-info bg-white rounded-3 shadow-sm p-4 mb-4">
                    <h1 class="video-title h3 mb-4" th:text="${video.title}">视频标题</h1>
                    
                    <div class="video-meta d-flex flex-wrap align-items-center gap-4 mb-4">
                        <span class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            <span th:text="${#temporals.format(video.createdTime, 'yyyy年MM月dd日')}">2024年01月01日</span>
                        </span>
                        <span class="text-muted" th:if="${video.resolution != null}">
                            <i class="fas fa-tv me-1"></i>
                            <span th:text="${video.resolution}">1080p</span>
                        </span>
                    </div>

                    <div class="video-actions d-flex gap-2 mb-4">
                        <button class="btn btn-outline-primary" onclick="shareVideo()">
                            <i class="fas fa-share me-1"></i>分享
                        </button>
                        <button class="btn btn-outline-secondary" onclick="toggleFullscreen()">
                            <i class="fas fa-expand me-1"></i>全屏
                        </button>
                        <a th:href="@{/admin/edit/{id}(id=${video.id})}" class="btn btn-outline-warning">
                            <i class="fas fa-edit me-1"></i>编辑
                        </a>
                    </div>

                    <div class="video-description" th:if="${video.description != null and !video.description.isEmpty()}">
                        <h5>视频描述</h5>
                        <p class="text-muted" th:text="${video.description}">视频描述内容</p>
                    </div>

                    <!-- 技术信息 -->
                    <div class="video-tech-info mt-4">
                        <h6 class="text-muted mb-2">技术信息</h6>
                        <div class="row g-3">
                            <div class="col-md-6" th:if="${video.videoFormat != null}">
                                <small class="text-muted d-block">视频格式</small>
                                <span th:text="${video.videoFormat.toUpperCase()}">MP4</span>
                            </div>
                            <div class="col-md-6" th:if="${video.fileSize != null}">
                                <small class="text-muted d-block">文件大小</small>
                                <span th:text="${#numbers.formatDecimal(video.fileSize / 1024.0 / 1024.0, 1, 2)} + ' MB'">0.00 MB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 导航栏修复JS -->
    <script src="/js/navbar-fix.js"></script>
    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.6.1/video.min.js"></script>
    <!-- 自定义JS -->
    <script src="/js/main.js"></script>
    <script src="/js/video-player.js"></script>

    <script th:inline="javascript">
        // 初始化视频播放器
        document.addEventListener('DOMContentLoaded', function() {
            const videoElement = document.getElementById('video-player');
            const videoUrl = videoElement.dataset.videoUrl;
            const videoId = videoElement.dataset.videoId;
            
            // 初始化Video.js播放器
            const player = videojs('video-player', {
                fluid: true,
                responsive: true,
                playbackRates: [0.5, 1, 1.25, 1.5, 2],
                controls: true,
                preload: 'metadata',
                html5: {
                    vhs: {
                        overrideNative: true
                    }
                }
            });

            // 播放事件监听
            player.ready(function() {
                console.log('视频播放器已准备就绪');

                // 错误处理
                player.on('error', function(e) {
                    console.error('视频播放出错:', e);
                    const error = player.error();
                    let errorMessage = '视频加载失败，请检查网络连接。';
                    
                    if (error) {
                        console.error('错误代码:', error.code);
                        console.error('错误信息:', error.message);
                        
                        switch (error.code) {
                            case 1:
                                errorMessage = '视频加载被中止。';
                                break;
                            case 2:
                                errorMessage = '网络错误，无法加载视频。';
                                break;
                            case 3:
                                errorMessage = '视频解码失败或格式不支持。';
                                break;
                            case 4:
                                errorMessage = '视频不存在或无法访问。';
                                break;
                        }
                    }
                    
                    // 移除可能存在的旧错误信息
                    const oldError = videoElement.parentNode.querySelector('.alert');
                    if (oldError) {
                        oldError.remove();
                    }
                    
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-danger mt-3';
                    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}<br><small class="text-muted">视频URL: ${videoUrl}</small>`;
                    videoElement.parentNode.appendChild(errorDiv);
                    
                    // 尝试重新加载视频
                    setTimeout(() => {
                        player.src({ type: 'video/mp4', src: videoUrl });
                        player.load();
                    }, 3000);
                });
            });
        });

        // 分享功能
        function shareVideo() {
            const url = window.location.href;
            const title = document.querySelector('.video-title').textContent;
            
            if (navigator.share) {
                navigator.share({
                    title: title,
                    url: url
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(url).then(function() {
                    alert('视频链接已复制到剪贴板！');
                });
            }
        }

        // 全屏功能
        function toggleFullscreen() {
            const player = videojs('video-player');
            if (player.isFullscreen()) {
                player.exitFullscreen();
            } else {
                player.requestFullscreen();
            }
        }
    </script>
</body>
</html>

