<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title th:text="${pageTitle}">视频播放器 - 首页</title>



    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" type="image/x-icon" href="/favicon.ico">

    <!-- 错误抑制器 - 必须在所有其他脚本之前加载 -->
    <script src="/js/error-suppressor.js"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/index-style.css" rel="stylesheet">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <ul class="navbar-nav d-flex flex-row mb-0">
                    <li class="nav-item">
                        <a class="nav-link active px-2" href="/">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/videos">
                            <i class="fas fa-video me-1"></i>视频
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-2" href="/admin">
                            <i class="fas fa-cog me-1"></i>管理
                        </a>
                    </li>
                </ul>

                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="d-flex search-form" action="/search" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword" placeholder="搜索...">
                        <button class="btn btn-outline-light btn-sm" type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                        <button class="btn btn-outline-light btn-sm ms-1" type="button" onclick="toggleSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                    </form>
                    <button class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">
        <!-- 欢迎横幅 -->
        <div class="hero-section bg-gradient text-dark rounded-3 p-5 mb-5">
            <div class="row">
                <div class="col-12 text-center">
                    <div class="title-with-logo">
                        <img src="/images/favicon01.png" alt="Logo" width="100" height="100">
                        <h1 class="display-4 fw-bold mb-4">佳茵轻康</h1>
                    </div>
                    <div class="lead-carousel">
                        <div class="lead-scroll-container">
                            <!-- 联系信息将立即加载 -->
                        </div>
                        <div class="lead-indicators">
                            <!-- 指示器将动态生成 -->
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- 热门视频 -->
        <section class="mb-5" th:if="${popularVideos != null and !popularVideos.isEmpty()}">
            <div class="mb-4">
                <h2 class="section-title">
                    <i class="fas fa-fire text-danger me-2"></i>教学视频
                </h2>
                <div class="mt-3">
                    <a href="/videos" class="btn btn-outline-primary">查看更多</a>
                </div>
            </div>
            
            <div class="row g-4">
                <div class="col-lg-3 col-md-4 col-sm-6" th:each="video : ${popularVideos}">
                    <div class="video-card card h-100 shadow-sm">
                        <div class="video-thumbnail position-relative">
                            <img th:src="${video.thumbnailUrl != null ? video.thumbnailUrl :'/images/jyqk-sos-01.png'}" 
                                 class="card-img-top" 
                                 th:alt="${video.title}"
                                 loading="lazy"
                                 onerror="this.src='/images/jyqk-sos-01.png'">
                            <div class="play-overlay">
                                <a th:href="@{/play/{id}(id=${video.id})}" class="play-btn">
                                    <i class="fas fa-play fa-2x"></i>
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title" th:text="${video.title}">佳茵轻康</h5>
                            <!-- <p class="video-description" th:text="${video.description}">视频描述</p> -->
                            <div class="video-stats">
                                <div class="video-date" th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 空状态 -->
        <div th:if="${popularVideos == null or popularVideos.isEmpty()}" 
             class="text-center py-5">
            <i class="fas fa-video fa-4x text-muted mb-4"></i>
            <h3 class="text-muted mb-4">暂无视频</h3>
            <p class="text-muted mb-4">还没有上传任何视频，点击下方按钮开始添加视频吧！</p>
            <a href="/admin/add" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>添加第一个视频
            </a>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <!-- <h5><i class="fas fa-play-circle me-2"></i>佳茵轻康</h5> -->
                    <p class="mb-0">轻康自然，享瘦生活。</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="mb-0">
                        <small>© 2025 加盟合作. <a href="/about" class="text-light text-decoration-none">联系我们</a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 返回顶部按钮 -->
    <button id="backToTop" class="btn btn-primary position-fixed back-to-top">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/navbar-fix.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/lead-carousel-db.js"></script>



    <!-- 指示器重置功能补丁 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 指示器重置功能补丁加载');

            // 等待轮播初始化
            setTimeout(function() {
                const carousel = window.leadCarousel;
                if (!carousel) {
                    console.warn('轮播实例未找到，重试中...');
                    setTimeout(arguments.callee, 1000);
                    return;
                }

                console.log('✅ 找到轮播实例，添加重置功能');

                // 添加默认索引属性（如果不存在）
                if (typeof carousel.defaultIndex === 'undefined') {
                    carousel.defaultIndex = 0;
                }

                // LeadCarouselDB 已经有 detectDefaultItem 方法，不需要重复添加

                // 添加重置方法（如果不存在）
                if (typeof carousel.resetToDefault !== 'function') {
                    carousel.resetToDefault = function() {
                        if (this.contactData.length === 0) {
                            return;
                        }

                        if (this.currentIndex !== this.defaultIndex) {
                            this.goToSlide(this.defaultIndex);
                        }
                        this.showResetFeedback();
                    };
                }

                // 添加反馈方法（如果不存在）
                if (typeof carousel.showResetFeedback !== 'function') {
                    carousel.showResetFeedback = function() {
                        const currentItem = this.items[this.currentIndex];
                        const middleIndicator = document.querySelector('.indicator.active');

                        if (currentItem) {
                            currentItem.classList.add('reset-feedback');
                            setTimeout(() => currentItem.classList.remove('reset-feedback'), 1000);
                        }

                        if (middleIndicator) {
                            middleIndicator.classList.add('reset-feedback');
                            setTimeout(() => middleIndicator.classList.remove('reset-feedback'), 1000);
                        }
                    };
                }

                // 重新绑定指示器事件
                const indicators = document.querySelectorAll('.indicator');
                console.log(`🎯 重新绑定${indicators.length}个指示器事件`);

                indicators.forEach((indicator, index) => {
                    // 移除现有事件监听器（通过克隆节点）
                    const newIndicator = indicator.cloneNode(true);
                    indicator.parentNode.replaceChild(newIndicator, indicator);

                    // 添加新的事件监听器
                    newIndicator.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        console.log(`🖱️ 指示器${index}被点击`);

                        if (index === 0) {
                            console.log('⬆️ 上一个');
                            carousel.previousSlide();
                        } else if (index === 1) {
                            console.log('🎯 重置到默认');
                            carousel.resetToDefault();
                        } else if (index === 2) {
                            console.log('⬇️ 下一个');
                            carousel.nextSlide();
                        }
                    });
                });

                // detectDefaultItem 已在 renderCarousel 中自动调用，不需要重复调用

                // 指示器重置功能补丁安装完成

            }, 2000);
        });
    </script>
</body>
</html>

