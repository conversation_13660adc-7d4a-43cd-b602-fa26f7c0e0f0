package com.example.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

/**
 * Lead联系信息实体类
 */
@Entity
@Table(name = "lead_contacts")
public class LeadContact {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    /**
     * 联系人姓名
     */
    @Column(name = "contact_name", nullable = false, length = 50)
    private String contactName;
    
    /**
     * 微信号
     */
    @Column(name = "wechat", length = 100)
    private String wechat;
    
    /**
     * 电话号码
     */
    @Column(name = "phone", length = 20)
    private String phone;
    
    /**
     * 抖音号
     */
    @Column(name = "douyin", length = 100)
    private String douyin;
    
    /**
     * 抖音昵称
     */
    @Column(name = "douyin_nickname", length = 100)
    private String douyinNickname;
    
    /**
     * 显示顺序
     */
    @Column(name = "display_order", nullable = false)
    private Integer displayOrder = 0;
    
    /**
     * 是否启用
     */
    @Column(name = "is_enabled", nullable = false)
    private Boolean isEnabled = true;
    
    /**
     * 是否为默认显示
     */
    @Column(name = "is_default", nullable = false)
    private Boolean isDefault = false;
    
    /**
     * 创建时间
     */
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    /**
     * 备注信息
     */
    @Column(name = "remarks", length = 500)
    private String remarks;
    
    // 构造函数
    public LeadContact() {
        this.createdAt = LocalDateTime.now();
    }
    
    public LeadContact(String contactName, String wechat, String phone, String douyin, String douyinNickname) {
        this();
        this.contactName = contactName;
        this.wechat = wechat;
        this.phone = phone;
        this.douyin = douyin;
        this.douyinNickname = douyinNickname;
    }
    
    // Getter和Setter方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getContactName() {
        return contactName;
    }
    
    public void setContactName(String contactName) {
        this.contactName = contactName;
    }
    
    public String getWechat() {
        return wechat;
    }
    
    public void setWechat(String wechat) {
        this.wechat = wechat;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getDouyin() {
        return douyin;
    }
    
    public void setDouyin(String douyin) {
        this.douyin = douyin;
    }
    
    public String getDouyinNickname() {
        return douyinNickname;
    }
    
    public void setDouyinNickname(String douyinNickname) {
        this.douyinNickname = douyinNickname;
    }
    
    public Integer getDisplayOrder() {
        return displayOrder;
    }
    
    public void setDisplayOrder(Integer displayOrder) {
        this.displayOrder = displayOrder;
    }
    
    public Boolean getIsEnabled() {
        return isEnabled;
    }
    
    public void setIsEnabled(Boolean isEnabled) {
        this.isEnabled = isEnabled;
    }
    
    public Boolean getIsDefault() {
        return isDefault;
    }
    
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public String getRemarks() {
        return remarks;
    }
    
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
    
    /**
     * 生成显示文本
     */
    public String getDisplayText() {
        StringBuilder sb = new StringBuilder();
        
        // 微信/电话部分
        if (wechat != null && !wechat.trim().isEmpty() && phone != null && !phone.trim().isEmpty()) {
            sb.append("微信/电话：").append(phone).append("【").append(contactName).append("】");
        } else if (phone != null && !phone.trim().isEmpty()) {
            sb.append("电话：").append(phone).append("【").append(contactName).append("】");
        } else if (wechat != null && !wechat.trim().isEmpty()) {
            sb.append("微信：").append(wechat).append("【").append(contactName).append("】");
        }
        
        // 抖音部分
        if (douyin != null && !douyin.trim().isEmpty()) {
            sb.append("&nbsp;抖音号：");
            if (douyinNickname != null && !douyinNickname.trim().isEmpty()) {
                sb.append(douyinNickname);
            } else {
                sb.append(douyin);
            }
        }
        
        return sb.toString();
    }
    
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return "LeadContact{" +
                "id=" + id +
                ", contactName='" + contactName + '\'' +
                ", phone='" + phone + '\'' +
                ", douyin='" + douyin + '\'' +
                ", displayOrder=" + displayOrder +
                ", isEnabled=" + isEnabled +
                '}';
    }
}
