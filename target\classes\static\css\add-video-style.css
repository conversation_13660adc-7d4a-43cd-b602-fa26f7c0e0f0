/**
 * 添加视频页面专用样式
 * Add Video Page Styles
 */

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

/* 表单容器 */
.form-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* 表单标题 */
.form-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* 表单字段 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
}

.form-select:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* 文本域 */
textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* 预览区域 */
.preview-section {
    display: none;
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
    border: 2px dashed #dee2e6;
}

.preview-section.show {
    display: block;
    animation: fadeInUp 0.5s ease-out;
}

.preview-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

.preview-card {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.preview-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: #e9ecef;
}

.preview-content {
    padding: 1rem;
}

.preview-video-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.preview-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.preview-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.85rem;
    color: #6c757d;
}

.preview-meta-item {
    display: flex;
    align-items: center;
}

.preview-meta-item i {
    margin-right: 0.25rem;
    color: #28a745;
}

/* 按钮样式 */
.btn-primary {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
}

.btn-secondary {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-outline-info {
    border-radius: 8px;
    border-width: 2px;
    font-weight: 600;
}

/* 帮助文本 */
.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 必填字段标识 */
.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 表单验证样式 */
.is-valid {
    border-color: #28a745;
}

.is-invalid {
    border-color: #dc3545;
}

.valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 文件上传区域 */
.file-upload-area {
    border: 2px dashed #ced4da;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #28a745;
    background: #e8f5e8;
}

.file-upload-area.dragover {
    border-color: #28a745;
    background: #e8f5e8;
    transform: scale(1.02);
}

.file-upload-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.file-upload-text {
    color: #495057;
    font-weight: 500;
}

/* 进度条 */
.upload-progress {
    margin-top: 1rem;
    display: none;
}

.progress {
    height: 8px;
    border-radius: 4px;
    background: #e9ecef;
}

.progress-bar {
    background: linear-gradient(45deg, #28a745, #20c997);
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-container {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .page-header {
        padding: 1.5rem 0;
        text-align: center;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .preview-section {
        padding: 1rem;
    }
    
    .btn-primary,
    .btn-secondary {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .preview-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 576px) {
    .form-container {
        padding: 1rem;
        margin: 0.5rem;
    }
    
    .page-header {
        padding: 1rem 0;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .file-upload-area {
        padding: 1.5rem 1rem;
    }
    
    .file-upload-icon {
        font-size: 2rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .form-container {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .form-title {
        color: #f7fafc;
        border-bottom-color: #4a5568;
    }
    
    .form-label {
        color: #e2e8f0;
    }
    
    .form-control,
    .form-select {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus,
    .form-select:focus {
        background: #4a5568;
        border-color: #28a745;
        color: #e2e8f0;
    }
    
    .preview-section {
        background: #4a5568;
        border-color: #718096;
    }
    
    .preview-card {
        background: #2d3748;
    }
    
    .preview-video-title {
        color: #f7fafc;
    }
    
    .preview-description {
        color: #a0aec0;
    }
    
    .preview-meta {
        color: #a0aec0;
    }
    
    .file-upload-area {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .file-upload-area:hover {
        background: #2d5a3d;
        border-color: #28a745;
    }
    
    .file-upload-icon {
        color: #a0aec0;
    }
}
