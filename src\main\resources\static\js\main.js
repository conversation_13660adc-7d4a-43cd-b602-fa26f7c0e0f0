/**
 * 视频播放器主要JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// 全局变量
let currentPlayer = null;

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    initializeBackToTop();
    initializeTooltips();
    initializeAlerts();
    initializeSearchForm();
    initializeLazyLoading();
}

/**
 * 初始化返回顶部按钮
 */
function initializeBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    if (!backToTopBtn) return;

    // 监听滚动事件
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });

    // 点击返回顶部
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * 初始化工具提示
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * 初始化警告框自动关闭
 */
function initializeAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        // 5秒后自动关闭成功提示
        if (alert.classList.contains('alert-success')) {
            setTimeout(function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }, 5000);
        }
    });
}

/**
 * 初始化搜索表单
 */
function initializeSearchForm() {
    const searchForms = document.querySelectorAll('form[action="/search"]');
    searchForms.forEach(function(form) {
        form.addEventListener('submit', function(e) {
            const input = form.querySelector('input[name="keyword"]');
            if (input && input.value.trim() === '') {
                e.preventDefault();
                showAlert('请输入搜索关键词', 'warning');
            }
        });
    });
}

/**
 * 初始化图片懒加载
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(function(img) {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理
        images.forEach(function(img) {
            img.src = img.dataset.src;
        });
    }
}

/**
 * 显示提示消息
 */
function showAlert(message, type = 'info', duration = 3000) {
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertContainer.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // 自动关闭
    setTimeout(function() {
        if (alertContainer.parentNode) {
            const bsAlert = new bootstrap.Alert(alertContainer);
            bsAlert.close();
        }
    }, duration);
}

/**
 * 获取警告图标
 */
function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时长
 */
function formatDuration(seconds) {
    if (!seconds || seconds < 0) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * 复制文本到剪贴板
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showAlert('已复制到剪贴板', 'success');
    } catch (err) {
        // 降级处理
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showAlert('已复制到剪贴板', 'success');
    }
}

/**
 * 防抖函数
 */
function debounce(func, wait, immediate) {
    let timeout;
    return function executedFunction() {
        const context = this;
        const args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * API请求封装
 */
class ApiClient {
    static async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
            },
        };
        
        const config = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }
            
            return data;
        } catch (error) {
            console.error('API请求错误:', error);
            throw error;
        }
    }
    
    static async get(url) {
        return this.request(url, { method: 'GET' });
    }
    
    static async post(url, data) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data),
        });
    }
    
    static async put(url, data) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data),
        });
    }
    
    static async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }
}

/**
 * 视频相关API
 */
class VideoApi {
    static async getVideos(page = 0, size = 10, sortBy = 'createdTime', sortDir = 'desc') {
        const params = new URLSearchParams({
            page: page.toString(),
            size: size.toString(),
            sortBy,
            sortDir
        });
        return ApiClient.get(`/api/videos?${params}`);
    }
    
    static async getVideo(id) {
        return ApiClient.get(`/api/videos/${id}`);
    }
    
    static async createVideo(videoData) {
        return ApiClient.post('/api/videos', videoData);
    }
    
    static async updateVideo(id, videoData) {
        return ApiClient.put(`/api/videos/${id}`, videoData);
    }
    
    static async deleteVideo(id) {
        return ApiClient.delete(`/api/videos/${id}`);
    }
    
    static async playVideo(id) {
        return ApiClient.post(`/api/videos/${id}/play`);
    }
    
    static async searchVideos(keyword, page = 0, size = 10) {
        const params = new URLSearchParams({
            keyword,
            page: page.toString(),
            size: size.toString()
        });
        return ApiClient.get(`/api/videos/search?${params}`);
    }
    
    static async getPopularVideos(limit = 10) {
        return ApiClient.get(`/api/videos/popular?limit=${limit}`);
    }
    
    
}

/**
 * 本地存储工具
 */
class StorageUtil {
    static set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('存储数据失败:', error);
        }
    }
    
    static get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    }
    
    static remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('删除数据失败:', error);
        }
    }
    
    static clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
        }
    }
}

/**
 * 表单验证工具
 */
class FormValidator {
    static validateRequired(value, fieldName) {
        if (!value || value.trim() === '') {
            throw new Error(`${fieldName}不能为空`);
        }
        return true;
    }
    
    static validateUrl(url, fieldName = 'URL') {
        if (!url) return false;
        
        try {
            new URL(url);
            return true;
        } catch {
            throw new Error(`${fieldName}格式不正确`);
        }
    }
    
    static validateVideoUrl(url) {
        this.validateUrl(url, '视频链接');
        
        const validExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm'];
        const hasValidExtension = validExtensions.some(ext => 
            url.toLowerCase().includes(ext)
        );
        
        const isOssUrl = url.toLowerCase().includes('aliyuncs.com') || 
                        url.toLowerCase().includes('oss-');
        
        if (!hasValidExtension && !isOssUrl) {
            throw new Error('请提供有效的视频链接');
        }
        
        return true;
    }
    
    static validateLength(value, maxLength, fieldName) {
        if (value && value.length > maxLength) {
            throw new Error(`${fieldName}长度不能超过${maxLength}个字符`);
        }
        return true;
    }
}

// 导出到全局作用域
window.VideoPlayer = {
    ApiClient,
    VideoApi,
    StorageUtil,
    FormValidator,
    showAlert,
    copyToClipboard,
    formatFileSize,
    formatDuration,
    debounce,
    throttle
};

