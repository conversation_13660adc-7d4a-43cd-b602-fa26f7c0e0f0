package com.example.controller;

import com.example.entity.LeadContact;
import com.example.service.LeadContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Lead联系信息控制器
 */
@RestController
@RequestMapping("/api/lead-contacts")
@CrossOrigin(origins = "*")
public class LeadContactController {
    
    @Autowired
    private LeadContactService leadContactService;
    
    /**
     * 获取轮播显示的联系信息
     */
    @GetMapping("/carousel")
    public ResponseEntity<Map<String, Object>> getCarouselContacts(
            @RequestParam(defaultValue = "3") int limit) {
        
        List<LeadContact> contacts = leadContactService.getCarouselContacts(limit);
        Optional<LeadContact> defaultContact = leadContactService.getDefaultContact();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", contacts);
        response.put("defaultContactId", defaultContact.map(LeadContact::getId).orElse(null));
        response.put("total", contacts.size());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取所有启用的联系信息
     */
    @GetMapping("/enabled")
    public ResponseEntity<Map<String, Object>> getAllEnabledContacts() {
        List<LeadContact> contacts = leadContactService.getAllEnabledContacts();
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", contacts);
        response.put("total", contacts.size());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 根据ID获取联系信息
     */
    @GetMapping("/{id}")
    public ResponseEntity<Map<String, Object>> getContactById(@PathVariable Long id) {
        Optional<LeadContact> contact = leadContactService.getContactById(id);
        
        Map<String, Object> response = new HashMap<>();
        if (contact.isPresent()) {
            response.put("success", true);
            response.put("data", contact.get());
        } else {
            response.put("success", false);
            response.put("message", "联系信息不存在");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 创建新的联系信息
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> createContact(@RequestBody LeadContact contact) {
        try {
            // 检查电话号码是否已存在
            if (contact.getPhone() != null && leadContactService.isPhoneExists(contact.getPhone())) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "电话号码已存在");
                return ResponseEntity.badRequest().body(response);
            }
            
            LeadContact savedContact = leadContactService.saveContact(contact);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", savedContact);
            response.put("message", "联系信息创建成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "创建失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 更新联系信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<Map<String, Object>> updateContact(
            @PathVariable Long id, @RequestBody LeadContact contact) {
        try {
            Optional<LeadContact> existingContact = leadContactService.getContactById(id);
            if (!existingContact.isPresent()) {
                Map<String, Object> response = new HashMap<>();
                response.put("success", false);
                response.put("message", "联系信息不存在");
                return ResponseEntity.notFound().build();
            }
            
            contact.setId(id);
            LeadContact updatedContact = leadContactService.updateContact(contact);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", updatedContact);
            response.put("message", "联系信息更新成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 禁用联系信息
     */
    @PutMapping("/{id}/disable")
    public ResponseEntity<Map<String, Object>> disableContact(@PathVariable Long id) {
        try {
            leadContactService.disableContact(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "联系信息已禁用");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "禁用失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 删除联系信息
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Map<String, Object>> deleteContact(@PathVariable Long id) {
        try {
            leadContactService.deleteContact(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "联系信息已删除");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "删除失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 设置默认联系信息
     */
    @PutMapping("/{id}/set-default")
    public ResponseEntity<Map<String, Object>> setDefaultContact(@PathVariable Long id) {
        try {
            leadContactService.setDefaultContact(id);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "默认联系信息设置成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "设置失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 批量更新显示顺序
     */
    @PutMapping("/batch-order")
    public ResponseEntity<Map<String, Object>> batchUpdateDisplayOrder(@RequestBody List<Long> contactIds) {
        try {
            leadContactService.batchUpdateDisplayOrder(contactIds);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "显示顺序更新成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "更新失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 搜索联系信息
     */
    @GetMapping("/search")
    public ResponseEntity<Map<String, Object>> searchContacts(@RequestParam String keyword) {
        List<LeadContact> contacts = leadContactService.searchByContactName(keyword);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("data", contacts);
        response.put("total", contacts.size());
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 初始化默认数据
     */
    @PostMapping("/init-default")
    public ResponseEntity<Map<String, Object>> initializeDefaultData() {
        try {
            leadContactService.initializeDefaultData();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "默认数据初始化成功");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "初始化失败：" + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
}
