package com.example.config;

import com.example.service.LeadContactService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 数据初始化器
 * 在应用启动时初始化默认的联系信息数据
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    @Autowired
    private LeadContactService leadContactService;
    
    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查是否需要初始化数据
            Long count = leadContactService.getEnabledContactCount();
            if (count == 0) {
                System.out.println("正在初始化默认联系信息数据...");
                leadContactService.initializeDefaultData();
                System.out.println("默认联系信息数据初始化完成！");
            } else {
                System.out.println("联系信息数据已存在，跳过初始化。当前数量：" + count);
            }
        } catch (Exception e) {
            System.err.println("初始化联系信息数据失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
