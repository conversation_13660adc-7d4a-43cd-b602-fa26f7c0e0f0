-- Lead联系信息表
CREATE TABLE IF NOT EXISTS lead_contacts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    contact_name VARCHAR(50) NOT NULL COMMENT '联系人姓名',
    wechat VARCHAR(100) COMMENT '微信号',
    phone VARCHAR(20) COMMENT '电话号码',
    douyin VARCHAR(100) COMMENT '抖音号',
    douyin_nickname VARCHAR(100) COMMENT '抖音昵称',
    display_order INT NOT NULL DEFAULT 0 COMMENT '显示顺序',
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    is_default BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为默认显示',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remarks VARCHAR(500) COMMENT '备注信息',
    
    INDEX idx_display_order (display_order),
    INDEX idx_is_enabled (is_enabled),
    INDEX idx_is_default (is_default),
    INDEX idx_contact_name (contact_name),
    INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lead联系信息表';

-- 插入默认数据
INSERT INTO lead_contacts (contact_name, wechat, phone, douyin, douyin_nickname, display_order, is_enabled, is_default, remarks) VALUES
('林佳', '18722880704', '18722880704', '黄林佳：皮皮管理', '黄林佳：皮皮管理', 1, TRUE, FALSE, '皮皮管理负责人'),
('黄超', '18057722960', '18057722960', '黄超(黄小燕弟弟)', '黄超(黄小燕弟弟)', 2, TRUE, TRUE, '主要联系人'),
('小班', '15908542510', '15908542510', '佳茵轻康SOS小班', '佳茵轻康SOS小班', 3, TRUE, FALSE, 'SOS小班负责人')
ON DUPLICATE KEY UPDATE
    contact_name = VALUES(contact_name),
    wechat = VALUES(wechat),
    phone = VALUES(phone),
    douyin = VALUES(douyin),
    douyin_nickname = VALUES(douyin_nickname),
    display_order = VALUES(display_order),
    is_enabled = VALUES(is_enabled),
    is_default = VALUES(is_default),
    remarks = VALUES(remarks);

-- 创建视图：启用的联系信息
CREATE OR REPLACE VIEW v_enabled_lead_contacts AS
SELECT 
    id,
    contact_name,
    wechat,
    phone,
    douyin,
    douyin_nickname,
    display_order,
    is_default,
    created_at,
    updated_at,
    remarks,
    CONCAT(
        CASE 
            WHEN wechat IS NOT NULL AND phone IS NOT NULL THEN CONCAT('微信/电话：', phone, '【', contact_name, '】')
            WHEN phone IS NOT NULL THEN CONCAT('电话：', phone, '【', contact_name, '】')
            WHEN wechat IS NOT NULL THEN CONCAT('微信：', wechat, '【', contact_name, '】')
            ELSE CONCAT('联系人：', contact_name)
        END,
        CASE 
            WHEN douyin IS NOT NULL THEN 
                CONCAT('&nbsp;抖音号：', COALESCE(douyin_nickname, douyin))
            ELSE ''
        END
    ) AS display_text
FROM lead_contacts 
WHERE is_enabled = TRUE
ORDER BY display_order ASC, id ASC;

-- 创建存储过程：重新排序显示顺序
DELIMITER //
CREATE PROCEDURE ReorderLeadContacts()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE contact_id BIGINT;
    DECLARE new_order INT DEFAULT 1;
    
    DECLARE contact_cursor CURSOR FOR 
        SELECT id FROM lead_contacts 
        WHERE is_enabled = TRUE 
        ORDER BY display_order ASC, id ASC;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN contact_cursor;
    
    read_loop: LOOP
        FETCH contact_cursor INTO contact_id;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        UPDATE lead_contacts 
        SET display_order = new_order 
        WHERE id = contact_id;
        
        SET new_order = new_order + 1;
    END LOOP;
    
    CLOSE contact_cursor;
END //
DELIMITER ;

-- 创建触发器：确保只有一个默认联系信息
DELIMITER //
CREATE TRIGGER tr_lead_contacts_default_check
    BEFORE UPDATE ON lead_contacts
    FOR EACH ROW
BEGIN
    IF NEW.is_default = TRUE AND OLD.is_default = FALSE THEN
        UPDATE lead_contacts 
        SET is_default = FALSE 
        WHERE id != NEW.id AND is_default = TRUE;
    END IF;
END //
DELIMITER ;

-- 创建触发器：新增默认联系信息时的检查
DELIMITER //
CREATE TRIGGER tr_lead_contacts_insert_default_check
    BEFORE INSERT ON lead_contacts
    FOR EACH ROW
BEGIN
    IF NEW.is_default = TRUE THEN
        UPDATE lead_contacts 
        SET is_default = FALSE 
        WHERE is_default = TRUE;
    END IF;
END //
DELIMITER ;

-- 创建函数：获取下一个显示顺序
DELIMITER //
CREATE FUNCTION GetNextDisplayOrder() RETURNS INT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE next_order INT DEFAULT 1;
    
    SELECT COALESCE(MAX(display_order), 0) + 1 
    INTO next_order 
    FROM lead_contacts;
    
    RETURN next_order;
END //
DELIMITER ;
