<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 移动端调试页面</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            padding: 10px; 
            font-size: 14px;
            background: #f8f9fa;
        }
        .debug-panel { 
            background: white; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-ok { color: #28a745; }
        .status-error { color: #dc3545; }
        .status-warning { color: #ffc107; }
        .log-item { 
            padding: 5px; 
            margin: 2px 0; 
            border-left: 3px solid #007bff; 
            background: #f8f9fa; 
            font-family: monospace;
            font-size: 12px;
        }
        .carousel-test {
            height: 120px;
            border: 2px dashed #ccc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h3>📱 移动端轮播调试</h3>
        
        <div class="debug-panel">
            <h5>🔍 设备信息</h5>
            <p><strong>用户代理:</strong> <span id="userAgent"></span></p>
            <p><strong>屏幕尺寸:</strong> <span id="screenSize"></span></p>
            <p><strong>网络状态:</strong> <span id="networkStatus"></span></p>
            <p><strong>设备类型:</strong> <span id="deviceType"></span></p>
        </div>
        
        <div class="debug-panel">
            <h5>🌐 API测试</h5>
            <button class="btn btn-primary btn-sm" onclick="testAPI()">测试API连接</button>
            <button class="btn btn-info btn-sm" onclick="testDataLoading()">测试数据加载</button>
            <button class="btn btn-success btn-sm" onclick="testCarousel()">测试轮播组件</button>
            <button class="btn btn-warning btn-sm" onclick="reloadPage()">重新加载页面</button>
            <div id="apiResults" class="mt-2"></div>
        </div>
        
        <div class="debug-panel">
            <h5>🎠 轮播测试区域</h5>
            <div class="carousel-test">
                <div class="lead-carousel" style="width: 100%; height: 100%; background: white; border-radius: 6px;">
                    <div class="lead-scroll-container">
                        <!-- 轮播内容将在这里显示 -->
                    </div>
                    <div class="lead-indicators">
                        <!-- 指示器将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
        
        <div class="debug-panel">
            <h5>📋 实时日志</h5>
            <button class="btn btn-secondary btn-sm" onclick="clearLogs()">清除日志</button>
            <div id="logContainer" style="max-height: 300px; overflow-y: auto; margin-top: 10px;">
                <!-- 日志将在这里显示 -->
            </div>
        </div>
    </div>

    <script src="/js/lead-carousel-db.js"></script>
    <script>
        let carousel;
        let logs = [];
        
        // 重写console.log来捕获日志
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        console.log = function(...args) {
            addLog('INFO', args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.warn = function(...args) {
            addLog('WARN', args.join(' '));
            originalWarn.apply(console, args);
        };
        
        console.error = function(...args) {
            addLog('ERROR', args.join(' '));
            originalError.apply(console, args);
        };
        
        function addLog(level, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ level, message, timestamp });
            updateLogDisplay();
        }
        
        function updateLogDisplay() {
            const container = document.getElementById('logContainer');
            container.innerHTML = logs.slice(-20).map(log => {
                const className = log.level === 'ERROR' ? 'status-error' : 
                                log.level === 'WARN' ? 'status-warning' : 'status-ok';
                return `<div class="log-item ${className}">[${log.timestamp}] ${log.level}: ${log.message}</div>`;
            }).join('');
            container.scrollTop = container.scrollHeight;
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
        
        // 初始化设备信息
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('userAgent').textContent = navigator.userAgent;
            document.getElementById('screenSize').textContent = `${window.innerWidth}x${window.innerHeight}`;
            document.getElementById('networkStatus').textContent = navigator.onLine ? '在线' : '离线';
            
            // 检测设备类型
            const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            document.getElementById('deviceType').textContent = isMobile ? '移动设备' : '桌面设备';
            
            addLog('INFO', '📱 移动端调试页面已加载');
        });
        
        // 测试API连接
        async function testAPI() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="text-info">正在测试API连接...</div>';

            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                try {
                    controller.abort();
                } catch (error) {
                    // 静默处理abort错误
                }
            }, 5000);

            try {
                const response = await fetch('/api/lead-contacts/all', {
                    signal: controller.signal,
                    headers: {
                        'Cache-Control': 'no-cache'
                    }
                });

                clearTimeout(timeoutId);
                const data = await response.json();

                if (response.ok && data.success) {
                    resultsDiv.innerHTML = `
                        <div class="status-ok">✅ API连接成功</div>
                        <div>数据数量: ${data.total}</div>
                        <div>默认联系人ID: ${data.defaultContactId || '无'}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="status-error">❌ API响应异常: ${data.message || '未知错误'}</div>`;
                }
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    resultsDiv.innerHTML = '<div class="status-error">❌ API请求超时</div>';
                } else {
                    resultsDiv.innerHTML = `<div class="status-error">❌ API连接失败: ${error.message}</div>`;
                }
            }
        }
        
        // 测试数据加载
        async function testDataLoading() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="text-info">正在测试数据加载...</div>';
            
            try {
                // 创建临时轮播实例进行测试
                const testCarousel = new LeadCarouselDB();
                const data = await testCarousel.loadContactData();
                
                if (data && data.length > 0) {
                    resultsDiv.innerHTML = `
                        <div class="status-ok">✅ 数据加载成功</div>
                        <div>联系人数量: ${data.length}</div>
                        <div>联系人列表: ${data.map(c => c.contactName).join(', ')}</div>
                    `;
                } else {
                    resultsDiv.innerHTML = '<div class="status-error">❌ 数据加载失败或数据为空</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status-error">❌ 数据加载异常: ${error.message}</div>`;
            }
        }
        
        // 测试轮播组件
        async function testCarousel() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="text-info">正在测试轮播组件...</div>';
            
            try {
                // 销毁现有轮播
                if (carousel) {
                    carousel.destroy();
                }
                
                // 创建新轮播
                carousel = new LeadCarouselDB();
                await carousel.init();
                
                resultsDiv.innerHTML = `
                    <div class="status-ok">✅ 轮播组件初始化成功</div>
                    <div>数据数量: ${carousel.contactData.length}</div>
                    <div>当前索引: ${carousel.currentIndex}</div>
                `;
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status-error">❌ 轮播组件测试失败: ${error.message}</div>`;
            }
        }
        
        // 监听网络状态变化
        window.addEventListener('online', () => {
            document.getElementById('networkStatus').textContent = '在线';
            addLog('INFO', '📱 网络已连接');
        });
        
        window.addEventListener('offline', () => {
            document.getElementById('networkStatus').textContent = '离线';
            addLog('WARN', '📱 网络已断开');
        });
        
        // 监听屏幕方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                document.getElementById('screenSize').textContent = `${window.innerWidth}x${window.innerHeight}`;
                addLog('INFO', '📱 屏幕方向已变化');
            }, 100);
        });

        // 重新加载页面
        function reloadPage() {
            addLog('INFO', '🔄 正在重新加载页面...');
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        }
    </script>
</body>
</html>
