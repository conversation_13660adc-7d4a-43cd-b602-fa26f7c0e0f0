/**
 * 编辑视频页面专用样式
 * Edit Video Page Styles
 */

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 15px 15px;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-header p {
    opacity: 0.9;
    margin-bottom: 0;
}

/* 表单容器 */
.form-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 15px rgba(0,0,0,0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

/* 表单标题 */
.form-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
}

/* 表单字段 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
}

.form-select:focus {
    border-color: #ffc107;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25);
}

/* 文本域 */
textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

/* 当前缩略图显示 */
.current-thumbnail {
    max-width: 200px;
    max-height: 120px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.current-thumbnail:hover {
    transform: scale(1.05);
}

/* 缩略图容器 */
.thumbnail-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 2px dashed #dee2e6;
    text-align: center;
}

.thumbnail-placeholder {
    color: #6c757d;
    font-style: italic;
}

/* 按钮样式 */
.btn-warning {
    background: linear-gradient(45deg, #ffc107, #ff8f00);
    border: none;
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
    color: white;
}

.btn-secondary {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-danger {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

.btn-outline-info {
    border-radius: 8px;
    border-width: 2px;
    font-weight: 600;
}

/* 状态切换 */
.form-check {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 1rem;
}

.form-check-input {
    width: 1.5rem;
    height: 1.5rem;
    margin-top: 0;
}

.form-check-input:checked {
    background-color: #ffc107;
    border-color: #ffc107;
}

.form-check-label {
    font-weight: 600;
    color: #495057;
    margin-left: 0.5rem;
}

/* 帮助文本 */
.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 必填字段标识 */
.required::after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* 表单验证样式 */
.is-valid {
    border-color: #28a745;
}

.is-invalid {
    border-color: #dc3545;
}

.valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 视频信息卡片 */
.video-info-card {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 2rem;
}

.video-info-title {
    color: #856404;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.video-info-text {
    color: #856404;
    margin-bottom: 0;
}

/* 操作按钮组 */
.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 2px solid #e9ecef;
}

.primary-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.secondary-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* 删除确认区域 */
.danger-zone {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.danger-zone-title {
    color: #721c24;
    font-weight: 600;
    margin-bottom: 1rem;
}

.danger-zone-text {
    color: #721c24;
    margin-bottom: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-container {
        padding: 1.5rem;
        margin: 1rem;
    }
    
    .page-header {
        padding: 1.5rem 0;
        text-align: center;
    }
    
    .page-header h1 {
        font-size: 1.75rem;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .primary-actions,
    .secondary-actions {
        width: 100%;
        justify-content: center;
    }
    
    .btn-warning,
    .btn-secondary,
    .btn-danger {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .current-thumbnail {
        max-width: 150px;
        max-height: 90px;
    }
}

@media (max-width: 576px) {
    .form-container {
        padding: 1rem;
        margin: 0.5rem;
    }
    
    .page-header {
        padding: 1rem 0;
    }
    
    .page-header h1 {
        font-size: 1.5rem;
    }
    
    .primary-actions,
    .secondary-actions {
        flex-direction: column;
    }
    
    .current-thumbnail {
        max-width: 120px;
        max-height: 72px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .form-container {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .form-title {
        color: #f7fafc;
        border-bottom-color: #4a5568;
    }
    
    .form-label {
        color: #e2e8f0;
    }
    
    .form-control,
    .form-select {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus,
    .form-select:focus {
        background: #4a5568;
        border-color: #ffc107;
        color: #e2e8f0;
    }
    
    .form-check {
        background: #4a5568;
        border-color: #718096;
    }
    
    .form-check-label {
        color: #e2e8f0;
    }
    
    .video-info-card {
        background: #744210;
        border-color: #975a16;
        color: #fef3c7;
    }
    
    .video-info-title {
        color: #fef3c7;
    }
    
    .video-info-text {
        color: #fef3c7;
    }
    
    .danger-zone {
        background: #742a2a;
        border-color: #9b2c2c;
    }
    
    .danger-zone-title,
    .danger-zone-text {
        color: #fed7d7;
    }
    
    .thumbnail-container {
        background: #4a5568;
        border-color: #718096;
    }
    
    .thumbnail-placeholder {
        color: #a0aec0;
    }
}
